---
title: 聊天模型
description: 聊天模型
keywords: [聊天]
sidebar_position: 2
---

"聊天模型" 是一个训练为在交谈格式中响应的 LLM 。因为它们应该回答通用问题和生成复杂代码，最好的聊天模型通常是很大的，通常 405B+ 参数。在 Continue 中，这些模型用来 [聊天](../../chat/how-to-use-it.md) ， [编辑](../../edit/how-to-use-it.md) 和 [Actions](../../actions/how-to-use-it.md) 。

## 推荐的聊天模型

如果你有使用任何模型的能力，我们推荐 [Claude 3.5 Sonnet](../model-providers/top-level/anthropic.md) 。

否则，以下是一些最好的选择：

- [GPT-4o](../model-providers/top-level/openai.md)
- [Gemini 2.0 Flash](../model-providers/top-level/gemini.md)
- [Llama3.1 405B](../tutorials/llama3.1.md)
